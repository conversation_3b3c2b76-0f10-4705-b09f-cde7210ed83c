import pytest
from django.test import TestCase
from unittest.mock import Mock

from estate.models import Customer, Affiliate<PERSON><PERSON><PERSON>, Partner


class TestCustomer(TestCase):
    """Test Customer model structure and methods."""

    def test_customer_model_fields(self):
        """Test Customer model field definitions."""
        # Test field existence and properties
        name_field = Customer._meta.get_field('name')
        self.assertEqual(name_field.max_length, 255)
        self.assertTrue(name_field.unique)
        self.assertTrue(name_field.null)

        storage_field = Customer._meta.get_field('storage')
        self.assertEqual(storage_field.default, "op")

        sftp_field = Customer._meta.get_field('sftp')
        self.assertEqual(sftp_field.default, 0)

        customer_type_field = Customer._meta.get_field('customerType')
        self.assertEqual(customer_type_field.default, 1)

        callback_url_field = Customer._meta.get_field('defaultCallbackURL')
        self.assertEqual(callback_url_field.default, "https://umony.com")

    def test_get_s3_settings_method(self):
        """Test get_s3_settings method with mock customer."""
        # Create a mock customer instance
        customer = Customer()
        customer.s3region = "us-east-1"
        customer.s3key = "test_key"
        customer.s3secret = "test_secret"
        customer.s3bucket = "test_bucket"
        customer.s3folder = "test_folder"

        s3_settings = customer.get_s3_settings()

        expected_settings = {
            "region_name": "us-east-1",
            "aws_access_key_id": "test_key",
            "aws_secret_access_key": "test_secret",
            "bucket_name": "test_bucket",
            "folder_name": "test_folder"
        }

        self.assertEqual(s3_settings, expected_settings)

    def test_get_s3_settings_with_none_values(self):
        """Test get_s3_settings method with None values."""
        customer = Customer()
        # All S3 fields default to None

        s3_settings = customer.get_s3_settings()

        expected_settings = {
            "region_name": None,
            "aws_access_key_id": None,
            "aws_secret_access_key": None,
            "bucket_name": None,
            "folder_name": None
        }

        self.assertEqual(s3_settings, expected_settings)

    def test_customer_meta_options(self):
        """Test Customer model meta options."""
        self.assertFalse(Customer._meta.managed)
        self.assertEqual(Customer._meta.db_table, "customer")
        self.assertEqual(Customer._meta.default_permissions, ("view",))


class TestAffiliateVendor(TestCase):
    """Test AffiliateVendor model structure."""

    def test_affiliate_vendor_model_fields(self):
        """Test AffiliateVendor model field definitions."""
        vendor_identity_field = AffiliateVendor._meta.get_field('vendorIdentity')
        self.assertEqual(vendor_identity_field.max_length, 255)
        self.assertTrue(vendor_identity_field.null)

        domain_field = AffiliateVendor._meta.get_field('domain')
        self.assertEqual(domain_field.max_length, 255)
        self.assertTrue(domain_field.null)

        notes_field = AffiliateVendor._meta.get_field('notes')
        self.assertTrue(notes_field.null)

        created_field = AffiliateVendor._meta.get_field('created')
        self.assertTrue(created_field.null)

    def test_affiliate_vendor_meta_options(self):
        """Test AffiliateVendor model meta options."""
        self.assertFalse(AffiliateVendor._meta.managed)
        self.assertEqual(AffiliateVendor._meta.db_table, "affiliate_vendor")
        self.assertEqual(AffiliateVendor._meta.default_permissions, ("view",))


class TestPartner(TestCase):
    """Test Partner model structure."""

    def test_partner_model_fields(self):
        """Test Partner model field definitions."""
        name_field = Partner._meta.get_field('name')
        self.assertEqual(name_field.max_length, 255)
        self.assertEqual(name_field.default, "")

        max_trail_field = Partner._meta.get_field('maxTrailDuration')
        self.assertTrue(max_trail_field.null)
        self.assertIsNone(max_trail_field.default)

        created_field = Partner._meta.get_field('created')
        self.assertTrue(created_field.null)
        self.assertIsNone(created_field.default)

        contract_start_field = Partner._meta.get_field('contractStartDate')
        self.assertTrue(contract_start_field.null)
        self.assertIsNone(contract_start_field.default)

    def test_partner_meta_options(self):
        """Test Partner model meta options."""
        self.assertFalse(Partner._meta.managed)
        self.assertEqual(Partner._meta.db_table, "partner")
        self.assertEqual(Partner._meta.default_permissions, ("view",))
