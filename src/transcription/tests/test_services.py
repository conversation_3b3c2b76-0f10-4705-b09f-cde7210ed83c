import unittest
from unittest.mock import patch, MagicMock

import pytest

from service.tests.factories import CODAServiceFactory
from transcription.services import TranscriptionModelService, TranscriptionService
from transcription.exceptions import TranscriptionServiceException


@patch("transcription.services.AudioSegment", autospec=True)
@patch("transcription.services.whisperx", autospec=True)
@patch("transcription.services.settings")
class TestTranscriptionModelService(unittest.TestCase):
    @patch("transcription.services.LLMModel.objects.filter")
    @pytest.mark.django_db
    def test_init_llm_model_with_none(self, mock_llm_filter, mock_settings, mock_whisperx, mock_audio_segment):
        mock_settings.WORKER_TYPE = "TRANSCRIPTION"
        mock_llm_model = MagicMock()
        mock_llm_model.model_name = "whisper_large_v2"
        mock_llm_model.generation_config = {"device": "cuda"}
        mock_llm_filter.return_value.first.return_value = mock_llm_model

        service = TranscriptionModelService()
        self.assertEqual(service.llm_model, mock_llm_model)
        mock_llm_filter.assert_called_with(model_name="whisper_large_v2")

    @patch("transcription.services.suppress_stdout")
    @patch("transcription.services.CODAService.objects.filter")
    @patch("transcription.services.LLMModel.objects.filter")
    @pytest.mark.django_db
    def test_load_model(self, mock_llm_filter, mock_filter, mock_suppress_stdout, mock_settings, mock_whisperx, mock_audio_segment):
        mock_settings.WORKER_TYPE = "TRANSCRIPTION"
        # Mock the load_model in whisperx
        mock_model = MagicMock()
        mock_whisperx.load_model.return_value = mock_model

        mock_coda_service = MagicMock()
        mock_filter.return_value.first.return_value = mock_coda_service
        mock_llm_model = MagicMock()
        mock_llm_model.model_name = "whisper_large_v2"
        mock_llm_filter.return_value.first.return_value = mock_llm_model

        service = TranscriptionModelService()
        service._load_model()

        self.assertEqual(service.model, mock_model)
        mock_whisperx.load_model.assert_called()

    @patch("transcription.services.CODAService.objects.filter")
    @patch("transcription.services.LLMModel.objects.filter")
    @pytest.mark.django_db
    def test_init_not_transcription_worker(self, mock_llm_filter, mock_filter, mock_whisperx, mock_audio_segment, mock_settings):
        # Set WORKER_TYPE to a different value
        mock_settings.WORKER_TYPE = "OTHER_TYPE"
        mock_coda_service = MagicMock()
        mock_filter.return_value.first.return_value = mock_coda_service
        mock_llm_filter.return_value.first.return_value = None  # No LLM model found

        with self.assertRaises(AttributeError) as context:
            TranscriptionModelService()

        self.assertIn("'NoneType' object has no attribute", str(context.exception))


class TestTranscriptionService(unittest.TestCase):
    @patch("transcription.services.CODAService.objects.filter")
    @pytest.mark.django_db
    def setUp(self, mock_filter):
        mock_coda_service = MagicMock()
        mock_coda_service.configuration.transcription_settings.reuse_existing = True
        mock_filter.return_value.first.return_value = mock_coda_service
        mock_model_service = MagicMock()
        self.service = TranscriptionService(model_service=mock_model_service)

    def test_get_metrics(self):
        self.service.duration_ms = 60000  # 1-minute audio
        self.service.total_words = 120

        metrics = self.service.get_metrics()
        self.assertEqual(metrics["length"], 60.0)
        self.assertEqual(metrics["words"], 120)
        self.assertEqual(metrics["words_per_min"], 120.0)
        self.assertEqual(metrics["words_per_sec"], 2.0)

    def test_get_metrics_negative_duration(self):
        self.service.duration_ms = -1
        self.service.total_words = 0

        with self.assertRaises(ValueError):
            self.service.get_metrics()

    @patch("common.tenant_clients.Customer.objects.get")
    @patch("transcription.services.TenantHelper.get_audio_file_from_s3")
    @patch("transcription.services.TranscriptionService.process_file")
    @patch("transcription.services.TenantHelper.store_transcription_to_s3")
    @patch("transcription.services.os.remove")
    @patch("transcription.services.transaction.atomic")
    @pytest.mark.skip(reason="Skip this test for now")
    def test_transcribe_success(
        self,
        mock_transaction,
        mock_remove,
        mock_store_transcription,
        mock_process_file,
        mock_get_audio_file,
        mock_get_customer,
        mock_whisperx,
        mock_audio_segment,
    ):
        # Mock the `Customer.objects.get` method to return a dummy Customer object
        mock_customer = MagicMock()
        mock_get_customer.return_value = mock_customer  # Simulate a customer being found

        # Mock processing results and behaviors
        mock_process_file.return_value = {
            "left": {"segments": []},
            "right": {"segments": []},
            "language": "en",
        }
        mock_get_audio_file.return_value = "/tmp/mock_audio.wav"
        mock_store_transcription.return_value = "s3_path"

        # Execute the method
        result = self.service.transcribe(parley_id=1, customer_id=1, output_language="en")

        # Validate the behavior of the method
        self.assertIsNotNone(result)
        mock_get_audio_file.assert_called_once()
        mock_process_file.assert_called_once()
        mock_store_transcription.assert_called_once()
        mock_remove.assert_called_once_with("/tmp/mock_audio.wav")

        # Also ensure the mocked Customer query was called with the correct ID
        mock_get_customer.assert_called_once_with(id=1)

    @patch("transcription.services.os.remove")
    @patch("transcription.services.AudioSegment")
    def test_transcribe_short_audio(self, mock_audio_segment, mock_remove):
        # Mock audio segment to return short duration
        mock_audio = MagicMock()
        mock_audio.__len__ = MagicMock(return_value=5000)  # 5 seconds
        mock_audio_segment.from_file.return_value = mock_audio

        self.service.minimum_duration_required = 10000  # 10 seconds

        with self.assertRaises(TranscriptionServiceException):
            self.service.process_file("/tmp/mock_audio.wav")
        mock_remove.assert_not_called()
