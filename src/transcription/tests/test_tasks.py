import unittest
from unittest.mock import patch, MagicMock

import pytest

from transcription.tasks import transcription_task
from transcription.services import TranscriptionService


class TestTranscriptionTask(unittest.TestCase):
    @patch("transcription.tasks.GPUTask")
    def test_transcription_task_service_injection(self, mock_gpu_task):
        """Ensure that TranscriptionService gets injected into `kwargs`."""
        mock_self = MagicMock()
        mock_args = ()
        mock_kwargs = {}

        # Call the task
        transcription_task(mock_self, *mock_args, **mock_kwargs)

        # Ensure that the service was correctly injected into `kwargs`
        _, injected_kwargs = mock_gpu_task.call_args
        self.assertEqual(injected_kwargs["service"], TranscriptionService)

    @patch("transcription.tasks.GPUTask")
    def test_transcription_task_with_metadata(self, mock_gpu_task):
        """Test transcription task with service metadata."""
        mock_self = MagicMock()
        mock_args = (1, 2, "en")  # parley_id, customer_id, language
        mock_kwargs = {"service_metadata": {"reuse_transcription": True}}

        # Mock GPUTask instance
        mock_task_instance = mock_gpu_task.return_value
        mock_task_instance.run = MagicMock()

        # Call the task
        transcription_task(mock_self, *mock_args, **mock_kwargs)

        # Verify GPUTask was created with correct arguments
        mock_gpu_task.assert_called_once()
        call_args, call_kwargs = mock_gpu_task.call_args

        # The call_args includes the mock_self as first argument
        self.assertEqual(call_args, (mock_self,) + mock_args)
        self.assertEqual(call_kwargs["service"], TranscriptionService)
        # The task parameter is the actual Celery task, not mock_self
        self.assertIn("task", call_kwargs)
        self.assertEqual(call_kwargs["service_metadata"], {"reuse_transcription": True})

        # Verify run was called
        mock_task_instance.run.assert_called_once()

    @patch("transcription.tasks.GPUTask")
    def test_transcription_task_error_handling(self, mock_gpu_task):
        """Test transcription task handles errors gracefully."""
        mock_self = MagicMock()
        mock_args = (1, 2, "en")
        mock_kwargs = {}

        # Mock GPUTask to raise an exception
        mock_gpu_task.side_effect = Exception("GPU task failed")

        # The task will actually raise the exception since it's a Celery task
        # and the exception handling is done by Celery's retry mechanism
        with self.assertRaises(Exception) as context:
            transcription_task(mock_self, *mock_args, **mock_kwargs)

        self.assertIn("GPU task failed", str(context.exception))
