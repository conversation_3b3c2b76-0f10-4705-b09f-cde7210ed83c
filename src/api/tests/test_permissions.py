import pytest
from django.test import TestCase
from rest_framework.permissions import AllowAny
from rest_framework.views import APIView
from rest_framework.test import APIRequestFactory

from api.permissions import PublicViewMixin


class TestPublicViewMixin(TestCase):
    """Test PublicViewMixin class."""
    
    def test_authentication_classes(self):
        """Test that authentication_classes is empty."""
        mixin = PublicViewMixin()
        self.assertEqual(mixin.authentication_classes, [])
    
    def test_permission_classes(self):
        """Test that permission_classes contains AllowAny."""
        mixin = PublicViewMixin()
        self.assertEqual(mixin.permission_classes, [AllowAny])
    
    def test_mixin_with_api_view(self):
        """Test PublicViewMixin when used with APIView."""
        
        class TestView(PublicViewMixin, APIView):
            def get(self, request):
                return {"message": "success"}
        
        view = TestView()
        
        # Check that the mixin properties are inherited
        self.assertEqual(view.authentication_classes, [])
        self.assertEqual(view.permission_classes, [AllowAny])
    
    def test_mixin_inheritance_order(self):
        """Test that mixin works correctly when inherited first."""
        
        class TestView(PublicViewMixin, APIView):
            pass
        
        view = TestView()
        
        # Verify the mixin takes precedence
        self.assertEqual(view.authentication_classes, [])
        self.assertEqual(view.permission_classes, [AllowAny])
    
    def test_mixin_can_be_overridden(self):
        """Test that mixin properties can be overridden in subclass."""
        
        from rest_framework.authentication import SessionAuthentication
        from rest_framework.permissions import IsAuthenticated
        
        class TestView(PublicViewMixin, APIView):
            authentication_classes = [SessionAuthentication]
            permission_classes = [IsAuthenticated]
        
        view = TestView()
        
        # Should use the overridden values
        self.assertEqual(view.authentication_classes, [SessionAuthentication])
        self.assertEqual(view.permission_classes, [IsAuthenticated])
