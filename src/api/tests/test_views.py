import pytest
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, MagicMock

from api.views import HealthCheckView


class TestHealthCheckView(APITestCase):
    """Test HealthCheckView API endpoint."""
    
    def setUp(self):
        """Set up test data."""
        self.url = reverse('health_check')
    
    @patch('api.views.get_system_health')
    def test_health_check_success(self, mock_get_system_health):
        """Test successful health check."""
        # Mock the health check service
        mock_get_system_health.return_value = {
            'status': 'ok',
            'database': 'ok',
            'rabbitmq': 'ok',
            'consumers': 'ok'
        }
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['status'], 'ok')
        self.assertEqual(response.json()['database'], 'ok')
        self.assertEqual(response.json()['rabbitmq'], 'ok')
        mock_get_system_health.assert_called_once()
    
    @patch('api.views.get_system_health')
    def test_health_check_unhealthy(self, mock_get_system_health):
        """Test health check when system is unhealthy."""
        # Mock unhealthy system
        mock_get_system_health.return_value = {
            'status': 'unhealthy',
            'database': 'unhealthy: Connection failed',
            'rabbitmq': 'ok',
            'consumers': 'ok'
        }
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['status'], 'unhealthy')
        self.assertIn('unhealthy', response.json()['database'])
        mock_get_system_health.assert_called_once()
    
    def test_health_check_method_not_allowed(self):
        """Test that only GET method is allowed."""
        response = self.client.post(self.url)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
        
        response = self.client.put(self.url)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
        
        response = self.client.delete(self.url)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
    
    def test_health_check_no_authentication_required(self):
        """Test that health check doesn't require authentication."""
        # This should work without any authentication
        response = self.client.get(self.url)
        # Should not return 401 or 403
        self.assertNotEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertNotEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    @patch('api.views.get_system_health')
    def test_health_check_returns_json(self, mock_get_system_health):
        """Test that health check returns JSON response."""
        mock_get_system_health.return_value = {'status': 'ok'}
        
        response = self.client.get(self.url)
        
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIsInstance(response.json(), dict)
    
    def test_view_class_properties(self):
        """Test HealthCheckView class properties."""
        view = HealthCheckView()
        
        # Should inherit from PublicViewMixin (no authentication required)
        self.assertEqual(view.authentication_classes, [])
        # Should allow any permissions
        from rest_framework.permissions import AllowAny
        self.assertEqual(len(view.permission_classes), 1)
        self.assertEqual(view.permission_classes[0], AllowAny)
