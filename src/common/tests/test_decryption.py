import unittest
import tempfile
import os
import pytest
from unittest.mock import patch, MagicMock

from common.decryption import FileCrypter
from estate.models import Customer


class TestFileCrypter(unittest.TestCase):
    def setUp(self):
        """
        Set up test dependencies before each test.
        """
        self.file_crypter = FileCrypter()

    def test_init(self):
        """
        Verify that the FileCrypter is initialized correctly.
        """
        self.assertIsNotNone(self.file_crypter)
        self.assertIsInstance(self.file_crypter, FileCrypter)

    @patch.object(FileCrypter, 'hkdf_sha256', return_value=b'MockedKey')
    def test_hkdf_sha256(self, mock_hkdf):
        """
        Example test to ensure hkdf_sha256 is called with the right arguments
        and returns expected data.
        """
        salt = b'salt'
        input_key_material = b'input_key'
        derived_key = self.file_crypter.hkdf_sha256(salt, input_key_material, length=32)
        mock_hkdf.assert_called_once_with(salt, input_key_material, length=32)
        self.assertEqual(derived_key, b'MockedKey')

    @patch.object(FileCrypter, 'aes_256_ctr_decrypt', return_value=b'DecryptedData')
    def test_aes_256_ctr_decrypt(self, mock_decrypt):
        """
        Example test for aes_256_ctr_decrypt method.
        """
        ciphertext = b'cipher'
        key = b'key'
        iv = b'iv'
        result = self.file_crypter.aes_256_ctr_decrypt(ciphertext, key, iv)
        mock_decrypt.assert_called_once_with(ciphertext, key, iv)
        self.assertEqual(result, b'DecryptedData')

    @patch.object(FileCrypter, 'decrypt_data', return_value=b'PlainData')
    def test_decrypt_data(self, mock_decrypt_data):
        """
        Example test for decrypt_data method.
        """
        encrypted_data = b'encrypted'
        result = self.file_crypter.decrypt_data(encrypted_data)
        mock_decrypt_data.assert_called_once_with(encrypted_data)
        self.assertEqual(result, b'PlainData')

    @patch.object(FileCrypter, 'decrypt_file')
    def test_decrypt_file(self, mock_decrypt_file):
        """
        Example test for decrypt_file method.
        """
        fake_path = '/path/to/encrypted_file'
        self.file_crypter.decrypt_file(fake_path)
        mock_decrypt_file.assert_called_once_with(fake_path)

    @patch.object(FileCrypter, 'get_clear_key', return_value=b'clear_key')
    def test_get_clear_key(self, mock_get_clear_key):
        """
        Example test for get_clear_key method.
        """
        clear_key = self.file_crypter.get_clear_key()
        mock_get_clear_key.assert_called_once()
        self.assertEqual(clear_key, b'clear_key')

    @patch.object(FileCrypter, 'get_decrypted_key', return_value=b'dec_key')
    def test_get_decrypted_key(self, mock_get_decrypted_key):
        """
        Example test for get_decrypted_key method.
        """
        dec_key = self.file_crypter.get_decrypted_key()
        mock_get_decrypted_key.assert_called_once()
        self.assertEqual(dec_key, b'dec_key')

    @patch.object(FileCrypter, 'get_encrypted_key', return_value=b'enc_key')
    def test_get_encrypted_key(self, mock_get_encrypted_key):
        """
        Example test for get_encrypted_key method.
        """
        enc_key = self.file_crypter.get_encrypted_key()
        mock_get_encrypted_key.assert_called_once()
        self.assertEqual(enc_key, b'enc_key')

    def test_generate_secure_password(self):
        """
        Test that generate_secure_password returns a valid password.
        """
        password = self.file_crypter.generate_secure_password()
        self.assertIsInstance(password, str)
        self.assertGreaterEqual(len(password), 8)  # Minimum password length

    @patch('common.decryption.FileCrypter.encrypt_file_uenc01')
    def test_encrypt_file_calls_correct_method(self, mock_encrypt_uenc01):
        """
        Test that encrypt_file calls the correct encryption method.
        """
        fake_path = '/path/to/file'
        customer_id = 123
        mock_encrypt_uenc01.return_value = '/path/to/file.uenc'

        result = self.file_crypter.encrypt_file(fake_path, customer_id)

        mock_encrypt_uenc01.assert_called_once_with(fake_path, customer_id)
        self.assertEqual(result, '/path/to/file.uenc')

    @patch('common.decryption.Customer.objects.get')
    @pytest.mark.django_db(databases=['default', 'tranquility_estate'])
    def test_get_customer_public_keys_no_customer(self, mock_get):
        """
        Test get_customer_public_keys when no customer is found.
        """
        from common.exceptions import EncryptException
        mock_get.side_effect = Customer.DoesNotExist()

        with self.assertRaises(EncryptException):
            self.file_crypter.get_customer_public_keys(123)

    def test_aes_256_ctr_encrypt_decrypt_roundtrip(self):
        """
        Test that encryption and decryption work together.
        """
        plaintext = b'test data for encryption'
        key = b'a' * 32  # 256-bit key
        iv = b'b' * 16   # 128-bit IV

        # Test encryption
        encrypted = self.file_crypter.aes_256_ctr_encrypt(plaintext, key, iv)
        self.assertIsInstance(encrypted, bytes)
        self.assertNotEqual(encrypted, plaintext)

        # Test decryption
        decrypted = self.file_crypter.aes_256_ctr_decrypt(encrypted, key, iv)
        self.assertEqual(decrypted, plaintext)

    @patch.object(FileCrypter, 'encrypt_file_uenc02')
    def test_encrypt_file_uenc02(self, mock_encrypt_file_uenc02):
        """
        Example test for encrypt_file_uenc02 method.
        """
        fake_path = '/path/to/plain_file'
        self.file_crypter.encrypt_file_uenc02(fake_path)
        mock_encrypt_file_uenc02.assert_called_once_with(fake_path)
