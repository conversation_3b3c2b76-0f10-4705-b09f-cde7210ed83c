import pytest
from django.test import TestCase
from django.db import models, connection
from django.test.utils import override_settings

from common.fields import MediumTextField


class TestModel(models.Model):
    """Test model for MediumTextField testing."""
    medium_text = MediumTextField()
    
    class Meta:
        app_label = 'common'


class TestMediumTextField(TestCase):
    """Test MediumTextField custom field."""
    
    def test_field_description(self):
        """Test field description."""
        field = MediumTextField()
        self.assertEqual(field.description, "MediumText")
    
    def test_db_type_mysql(self):
        """Test db_type returns MEDIUMTEXT for MySQL."""
        field = MediumTextField()
        
        # Mock a MySQL connection
        class MockConnection:
            vendor = 'mysql'
        
        mock_connection = MockConnection()
        db_type = field.db_type(mock_connection)
        self.assertEqual(db_type, "MEDIUMTEXT")
    
    def test_db_type_other_database(self):
        """Test db_type returns MEDIUMTEXT for any database."""
        field = MediumTextField()
        
        # Mock a PostgreSQL connection
        class MockConnection:
            vendor = 'postgresql'
        
        mock_connection = MockConnection()
        db_type = field.db_type(mock_connection)
        self.assertEqual(db_type, "MEDIUMTEXT")
    
    def test_field_inheritance(self):
        """Test that MediumTextField inherits from Field."""
        field = MediumTextField()
        self.assertIsInstance(field, models.Field)
    
    def test_field_in_model(self):
        """Test that MediumTextField can be used in a model."""
        # Get the field from the test model
        field = TestModel._meta.get_field('medium_text')
        self.assertIsInstance(field, MediumTextField)
        self.assertEqual(field.description, "MediumText")
    
    def test_field_creation_with_options(self):
        """Test field creation with standard field options."""
        field = MediumTextField(
            null=True,
            blank=True,
            help_text="Test help text"
        )
        
        self.assertTrue(field.null)
        self.assertTrue(field.blank)
        self.assertEqual(field.help_text, "Test help text")
    
    def test_field_default_options(self):
        """Test field default options."""
        field = MediumTextField()
        
        # Default Field options
        self.assertFalse(field.null)
        self.assertFalse(field.blank)
        self.assertEqual(field.help_text, "")
