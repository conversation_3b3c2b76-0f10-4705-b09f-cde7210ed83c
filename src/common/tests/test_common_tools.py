import pytest
import json
import tempfile
import os
import sys
from unittest.mock import patch, MagicMock
from uuid import UUID
from django.test import TestCase

from common.tools import (
    UUIDEncoder, 
    update_nested_dict, 
    compress_file, 
    decompress_file,
    string_to_valid_filename,
    suppress_stdout,
    GPUMemoryDetails,
    get_queued_jobs,
    revoke_tasks_by_name
)


class TestUUIDEncoder(TestCase):
    """Test UUIDEncoder JSON encoder."""
    
    def test_uuid_encoding(self):
        """Test that UUID objects are encoded as hex strings."""
        test_uuid = UUID('12345678-1234-5678-1234-************')
        encoder = UUIDEncoder()
        
        result = encoder.default(test_uuid)
        self.assertEqual(result, '1234************1234************')
    
    def test_non_uuid_encoding(self):
        """Test that non-UUID objects fall back to default encoding."""
        encoder = UUIDEncoder()
        
        with self.assertRaises(TypeError):
            encoder.default("not a uuid")
    
    def test_json_dumps_with_uuid(self):
        """Test JSON serialization with UUID objects."""
        test_uuid = UUID('12345678-1234-5678-1234-************')
        data = {"id": test_uuid, "name": "test"}
        
        result = json.dumps(data, cls=UUIDEncoder)
        expected = '{"id": "1234************1234************", "name": "test"}'
        self.assertEqual(result, expected)


class TestUpdateNestedDict(TestCase):
    """Test update_nested_dict function."""
    
    def test_simple_update(self):
        """Test simple dictionary update."""
        original = {"a": 1, "b": 2}
        updates = {"b": 3, "c": 4}
        
        update_nested_dict(original, updates)
        
        expected = {"a": 1, "b": 3, "c": 4}
        self.assertEqual(original, expected)
    
    def test_nested_update(self):
        """Test nested dictionary update."""
        original = {"a": {"x": 1, "y": 2}, "b": 3}
        updates = {"a": {"y": 5, "z": 6}, "c": 7}
        
        update_nested_dict(original, updates)
        
        expected = {"a": {"x": 1, "y": 5, "z": 6}, "b": 3, "c": 7}
        self.assertEqual(original, expected)
    
    def test_deep_nested_update(self):
        """Test deeply nested dictionary update."""
        original = {"a": {"b": {"c": 1}}}
        updates = {"a": {"b": {"d": 2}}}
        
        update_nested_dict(original, updates)
        
        expected = {"a": {"b": {"c": 1, "d": 2}}}
        self.assertEqual(original, expected)
    
    def test_overwrite_non_dict(self):
        """Test overwriting non-dict values."""
        original = {"a": 1}
        updates = {"a": {"b": 2}}
        
        update_nested_dict(original, updates)
        
        expected = {"a": {"b": 2}}
        self.assertEqual(original, expected)


class TestFileCompression(TestCase):
    """Test file compression and decompression functions."""
    
    def setUp(self):
        """Set up temporary files for testing."""
        self.test_content = b"This is test content for compression testing."
        
    def test_compress_file_success(self):
        """Test successful file compression."""
        with tempfile.NamedTemporaryFile(delete=False) as input_file:
            input_file.write(self.test_content)
            input_file_path = input_file.name

        with tempfile.NamedTemporaryFile(delete=False) as output_file:
            output_file_path = output_file.name

        try:
            result = compress_file(input_file_path, output_file_path)
            self.assertTrue(result)
            self.assertTrue(os.path.exists(output_file_path))

            # For small files, compression might not reduce size due to overhead
            # Just verify the file was created and has content
            output_size = os.path.getsize(output_file_path)
            self.assertGreater(output_size, 0)

        finally:
            os.unlink(input_file_path)
            os.unlink(output_file_path)
    
    def test_compress_file_with_base64(self):
        """Test file compression with base64 encoding."""
        with tempfile.NamedTemporaryFile(delete=False) as input_file:
            input_file.write(self.test_content)
            input_file_path = input_file.name
        
        with tempfile.NamedTemporaryFile(delete=False) as output_file:
            output_file_path = output_file.name
        
        try:
            result = compress_file(input_file_path, output_file_path, base_64=True)
            self.assertTrue(result)
            
            # Read the output and verify it's base64 encoded
            with open(output_file_path, 'rb') as f:
                content = f.read()
                # Base64 content should be ASCII
                content.decode('ascii')
                
        finally:
            os.unlink(input_file_path)
            os.unlink(output_file_path)
    
    def test_decompress_file_success(self):
        """Test successful file decompression."""
        with tempfile.NamedTemporaryFile(delete=False) as input_file:
            input_file.write(self.test_content)
            input_file_path = input_file.name
        
        with tempfile.NamedTemporaryFile(delete=False) as compressed_file:
            compressed_file_path = compressed_file.name
        
        with tempfile.NamedTemporaryFile(delete=False) as output_file:
            output_file_path = output_file.name
        
        try:
            # First compress
            compress_file(input_file_path, compressed_file_path)
            
            # Then decompress
            decompress_file(compressed_file_path, output_file_path)
            
            # Verify content matches
            with open(output_file_path, 'rb') as f:
                decompressed_content = f.read()
            
            self.assertEqual(decompressed_content, self.test_content)
            
        finally:
            os.unlink(input_file_path)
            os.unlink(compressed_file_path)
            os.unlink(output_file_path)


class TestStringToValidFilename(TestCase):
    """Test string_to_valid_filename function."""
    
    def test_invalid_characters_replacement(self):
        """Test replacement of invalid characters."""
        input_string = 'file<name>with:invalid"chars/\\|?*'
        result = string_to_valid_filename(input_string)
        expected = 'file_name_with_invalid_chars_____'  # 5 underscores for the 5 invalid chars at end
        self.assertEqual(result, expected)
    
    def test_spaces_replacement(self):
        """Test replacement of spaces with underscores."""
        input_string = 'file name with spaces'
        result = string_to_valid_filename(input_string)
        expected = 'file_name_with_spaces'
        self.assertEqual(result, expected)
    
    def test_reserved_names(self):
        """Test handling of Windows reserved names."""
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'LPT1']
        
        for name in reserved_names:
            result = string_to_valid_filename(name)
            self.assertEqual(result, f'__{name}__')
            
            # Test case insensitive
            result_lower = string_to_valid_filename(name.lower())
            self.assertEqual(result_lower, f'__{name.lower()}__')
    
    def test_max_length_truncation(self):
        """Test filename length truncation."""
        long_string = 'a' * 300
        result = string_to_valid_filename(long_string, max_length=100)
        self.assertEqual(len(result), 100)
        self.assertEqual(result, 'a' * 100)
    
    def test_trailing_periods_and_spaces(self):
        """Test removal of trailing periods and spaces."""
        input_string = 'filename. . '
        result = string_to_valid_filename(input_string)
        # Spaces are replaced with underscores first, then trailing periods and spaces are stripped
        self.assertEqual(result, 'filename._._')


class TestSuppressStdout(TestCase):
    """Test suppress_stdout context manager."""
    
    def test_stdout_suppression(self):
        """Test that stdout is suppressed within context."""
        original_stdout = sys.stdout
        
        with suppress_stdout():
            print("This should not appear")
            current_stdout = sys.stdout
            self.assertNotEqual(current_stdout, original_stdout)
        
        # Verify stdout is restored
        self.assertEqual(sys.stdout, original_stdout)


class TestGPUMemoryDetails(TestCase):
    """Test GPUMemoryDetails class."""

    @patch('builtins.__import__')
    def test_gpu_available(self, mock_import):
        """Test GPUMemoryDetails when CUDA is available."""
        mock_torch = MagicMock()
        mock_torch.cuda.is_available.return_value = True
        mock_torch.cuda.memory_allocated.return_value = 1000000  # 1MB in bytes
        mock_torch.cuda.max_memory_allocated.return_value = 2000000  # 2MB in bytes
        mock_torch.cuda.memory_reserved.return_value = 3000000  # 3MB in bytes
        mock_torch.cuda.mem_get_info.return_value = (4000000, 8000000)  # (free, total)

        def import_side_effect(name, *args, **kwargs):
            if name == 'torch':
                return mock_torch
            return __import__(name, *args, **kwargs)

        mock_import.side_effect = import_side_effect

        gpu_details = GPUMemoryDetails()

        self.assertTrue(gpu_details.cuda)
        self.assertEqual(gpu_details.memory_allocated, 1.0)  # 1MB
        self.assertEqual(gpu_details.max_memory_allocated, 2.0)  # 2MB
        self.assertEqual(gpu_details.memory_reserved, 3.0)  # 3MB
        self.assertEqual(gpu_details.free, 4.0)  # 4MB

    @patch('builtins.__import__')
    def test_gpu_not_available(self, mock_import):
        """Test GPUMemoryDetails when CUDA is not available."""
        mock_torch = MagicMock()
        mock_torch.cuda.is_available.return_value = False

        def import_side_effect(name, *args, **kwargs):
            if name == 'torch':
                return mock_torch
            return __import__(name, *args, **kwargs)

        mock_import.side_effect = import_side_effect

        gpu_details = GPUMemoryDetails()

        self.assertFalse(gpu_details.cuda)
        self.assertEqual(gpu_details.memory_allocated, 0)
        self.assertEqual(gpu_details.max_memory_allocated, 0)
        self.assertEqual(gpu_details.memory_reserved, 0)
        self.assertEqual(gpu_details.free, 0)

    @patch('builtins.__import__')
    def test_torch_import_error(self, mock_import):
        """Test GPUMemoryDetails when torch is not available."""
        def import_side_effect(name, *args, **kwargs):
            if name == 'torch':
                raise ImportError("No module named 'torch'")
            return __import__(name, *args, **kwargs)

        mock_import.side_effect = import_side_effect

        gpu_details = GPUMemoryDetails()
        self.assertFalse(gpu_details.cuda)

    @patch('builtins.__import__')
    def test_str_representation_with_cuda(self, mock_import):
        """Test string representation when CUDA is available."""
        mock_torch = MagicMock()
        mock_torch.cuda.is_available.return_value = True
        mock_torch.cuda.memory_allocated.return_value = 1000000
        mock_torch.cuda.max_memory_allocated.return_value = 2000000
        mock_torch.cuda.memory_reserved.return_value = 3000000
        mock_torch.cuda.mem_get_info.return_value = (4000000, 8000000)

        def import_side_effect(name, *args, **kwargs):
            if name == 'torch':
                return mock_torch
            return __import__(name, *args, **kwargs)

        mock_import.side_effect = import_side_effect

        gpu_details = GPUMemoryDetails()
        str_repr = str(gpu_details)

        self.assertIn("Memory:", str_repr)
        self.assertIn("allocated: 1.0MB", str_repr)
        self.assertIn("max: 2.0MB", str_repr)
        self.assertIn("reserved: 3.0MB", str_repr)
        self.assertIn("free: 4.0MB", str_repr)

    @patch('builtins.__import__')
    def test_str_representation_without_cuda(self, mock_import):
        """Test string representation when CUDA is not available."""
        def import_side_effect(name, *args, **kwargs):
            if name == 'torch':
                raise ImportError("No module named 'torch'")
            return __import__(name, *args, **kwargs)

        mock_import.side_effect = import_side_effect

        gpu_details = GPUMemoryDetails()
        str_repr = str(gpu_details)
        self.assertEqual(str_repr, "")
