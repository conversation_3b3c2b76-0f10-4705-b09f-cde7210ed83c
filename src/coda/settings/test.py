"""
Django staging settings configuration file.

This file contains settings specific to the staging environment.
It includes sensitive information such as SECRET_KEY and database
configuration. It also configures Celery to use AWS SQS with specified
queue name mappings and broker transport options.
"""

from coda.settings.base import *  # noqa: F403, F401
from decouple import config


# SECURITY WARNING: keep the secret key used in production secret!
# This will raise an error if not set
SECRET_KEY = config("SECRET_KEY", "sdakjfhaskjfhkjashdfkjahfjkda")

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": ":memory:",
    },
    "tranquility_estate": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": ":memory:",
    },
    "tranquility_1": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": ":memory:",
    },
}

# Test-specific settings
DEBUG = True
TESTING = True

# Disable migrations for faster tests
class DisableMigrations:
    def __contains__(self, item):
        return True

    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

# Disable logging during tests
LOGGING_CONFIG = None

# Use in-memory file storage for tests
DEFAULT_FILE_STORAGE = 'django.core.files.storage.InMemoryStorage'

# Disable Celery for tests
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# Test email backend
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

# Use a simple password hasher for faster tests
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# Test worker settings
WORKER_TYPE = "TEST"

# Test timezone
USE_TZ = True
TIME_ZONE = 'UTC'
