import pytest
from django.contrib.auth.models import User
from unittest.mock import patch, MagicMock

from service.models import UserProfile, ServiceConfiguration, CODAService


@pytest.mark.django_db
class TestUserProfileSignals:
    def test_create_user_profile_signal(self):
        """Test that creating a user also creates a UserProfile via the signal."""
        user = User.objects.create_user(username="testuser", password="password")
        assert user.profile is not None
        assert isinstance(user.profile, UserProfile)

    def test_save_user_profile_signal(self):
        """Test that saving the user also saves the associated UserProfile."""
        user = User.objects.create_user(username="testuser", password="password")
        # Modify the profile and save the user
        user.profile.use_tenant_id = 42
        user.save()
        # Fetch the profile again and confirm the changes
        assert UserProfile.objects.get(user=user).use_tenant_id == 42


@pytest.mark.django_db
class TestServiceConfigurationQuerySet:
    def test_get_by_name(self):
        """Ensure ServiceConfiguration get_by_name works as expected."""
        config = ServiceConfiguration.objects.create(name="TestConfig")
        retrieved = ServiceConfiguration.objects.get_by_name("TestConfig")
        assert retrieved == config

    def test_get_by_name_not_found(self):
        """Ensure get_by_name returns None for missing configuration."""
        assert ServiceConfiguration.objects.get_by_name("NonExistent") is None


@pytest.mark.django_db
class TestServiceConfigurationProperties:
    @patch("llm_model.models.LLMPromptTemplate.objects.filter")
    def test_summary_default_prompt_id(self, mock_filter):
        """Test that summary_default_prompt_id returns the correct id."""
        mock_prompt = MagicMock()
        mock_prompt.id = 123
        mock_filter.return_value.first.return_value = mock_prompt
        config = ServiceConfiguration.objects.create(name="TestConfig")
        assert config.summary_default_prompt_id == "123"

    def test_transcription_settings_default(self):
        """Test default transcription settings."""
        config = ServiceConfiguration.objects.create(name="TestConfig")
        settings = config.transcription_settings
        assert settings.batch_size == 16  # Actual default value
        assert settings.reuse_existing is True
        # whisper_settings is not in the default settings

    def test_transcription_settings_override(self):
        """Test overridden transcription settings."""
        config = ServiceConfiguration.objects.create(
            name="TestConfig",
            service_settings={"transcription": {"batch_size": 32, "reuse_existing": False, "whisper_settings": {"device": "cpu"}}},
        )
        settings = config.transcription_settings
        assert settings.batch_size == 32
        assert settings.reuse_existing is False  # Overridden value
        assert settings.whisper_settings["device"] == "cpu"

    @patch("llm_model.models.LLMPromptTemplate.objects.get")
    @pytest.mark.skip(reason="Skip this test for now")
    def test_summary_prompt(self, mock_get):
        """Ensure summary_prompt fetches the correct prompt."""
        mock_prompt = MagicMock()
        mock_get.return_value = mock_prompt
        config = ServiceConfiguration.objects.create(name="TestConfig")
        prompt = config.summary_prompt
        assert prompt == mock_prompt


@pytest.mark.django_db
class TestCODAService:
    def test_model_str(self):
        """Ensure __str__ works for models."""
        config = ServiceConfiguration.objects.create(name="TestConfig")
        coda_service = CODAService.objects.create(name="TestService", configuration=config, enabled=True)
        assert str(coda_service) == "TestService"
