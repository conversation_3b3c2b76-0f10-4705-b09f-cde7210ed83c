# tests/factories.py

import factory
from faker import Faker
from django.utils import timezone
from tenant.models import ParleyTranscription, Parley  # Replace 'your_app' with your actual app name

fake = Faker()


class BaseTenantFactory(factory.django.DjangoModelFactory):
    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        # Extract the customer from kwargs or use a default one
        customer = kwargs.pop("customer", 1)

        # Create the instance without saving
        obj = model_class(*args, **kwargs)

        # Use the custom save method
        obj.save_for_customer(customer_id=customer)

        return obj


class ParleyFactory(BaseTenantFactory):
    class Meta:
        model = Parley

    owner = factory.Faker("random_int", min=1, max=100)
    group_id = factory.Faker("random_int", min=1, max=100)
    service = factory.Faker("random_int", min=1, max=100)
    service_code = factory.Faker("random_int", min=1, max=100)
    caller = factory.Faker("random_int", min=1, max=10000)
    caller_contact_id = factory.Faker("random_int", min=1, max=100)
    callee = factory.Faker("random_int", min=1, max=100)
    callee_contact_id = factory.Faker("random_int", min=1, max=100)
    localsystemid = factory.Faker("random_int", min=1, max=100)
    localsystemname = factory.Faker("company")
    chat_title = factory.Faker("sentence")
    localsystemmeta = factory.Faker("json")
    status = factory.Faker("random_int", min=1, max=100)
    url = factory.Faker("url")
    participants = factory.Faker("random_int", min=1, max=10)
    max_participants = factory.Faker("random_int", min=10, max=100)
    length = factory.Faker("random_int", min=60, max=3600)
    jsondatapack = factory.Faker("json")
    tag1 = factory.Faker("boolean")
    tag2 = factory.Faker("boolean")
    tag3 = factory.Faker("boolean")
    tag4 = factory.Faker("boolean")
    tag5 = factory.Faker("boolean")
    tag6 = factory.Faker("boolean")
    tag7 = factory.Faker("boolean")
    hunttime = factory.Faker("random_int", min=1, max=100000)
    ringtime = factory.Faker("random_int", min=1, max=100000)
    starttime = factory.Faker("date_time", tzinfo=timezone.get_current_timezone())
    lasteventtime = factory.Faker("date_time", tzinfo=timezone.get_current_timezone())
    endtime = factory.Faker("date_time", tzinfo=timezone.get_current_timezone())
    aendreason = factory.Faker("word")
    bendreason = factory.Faker("word")
    expirytime = factory.Faker("date_time", tzinfo=timezone.get_current_timezone())
    asr = factory.Faker("word")
    transcribed = factory.Faker("random_int", min=1, max=1000)
    recordedmedia = factory.Faker("boolean")
    mediatype = factory.Faker("mime_type")
    mediahmac = factory.Faker("sha256")
    medialocation = factory.Faker("url")
    retainuntil = factory.Faker("date_time", tzinfo=timezone.get_current_timezone())
    retention_policy = factory.Faker("random_int", min=1, max=10000)
    litigationhold = factory.Faker("boolean")
    last_update = factory.Faker("date_time", tzinfo=timezone.get_current_timezone())


class ParleyTranscriptionFactory(BaseTenantFactory):
    class Meta:
        model = ParleyTranscription

    # parley = factory.SubFactory(ParleyFactory)
    parley_id = factory.Faker("random_int", min=100, max=2000)
    language = factory.Faker("language_code")
    summary = factory.Faker("sentence")
    red_flag = factory.Faker("boolean")
    transcription_path = factory.Faker("file_path")
    length = factory.Faker("random_int", min=60, max=3600)
    words = factory.Faker("random_int", min=100, max=1000)
    words_per_min = factory.Faker("random_int", min=100, max=200)
    words_per_sec = factory.Faker("random_int", min=2, max=5)
    avg_score = factory.Faker("pyfloat", left_digits=2, right_digits=2, positive=True)
    transcribed_at = factory.Faker("date_time", tzinfo=timezone.get_current_timezone())
    created_at = factory.Faker("date_time", tzinfo=timezone.get_current_timezone())
    updated_at = factory.Faker("date_time", tzinfo=timezone.get_current_timezone())
    deleted_at = factory.Faker("date_time", tzinfo=timezone.get_current_timezone())
