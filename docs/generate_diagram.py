"""
Helper that generates the diagram
Docs: https://diagrams.mingrammer.com/docs
requirements:
pip install diagrams
brew install graphviz

"""
from diagrams import Diagram, Cluster
from diagrams.aws.storage import S3Glacier, S3
from diagrams.c4 import Relationship
from diagrams.custom import Custom
from diagrams.onprem.queue import RabbitMQ
from diagrams.onprem.client import Client
from diagrams.onprem.compute import Server
from diagrams.programming.framework import Django
from diagrams.generic.storage import Storage
from diagrams.aws.general import General
from diagrams.oci.governance import Policies
from diagrams.aws.database import RDSMysqlInstance, Aurora, RDS

with Diagram("CODA LLD Architecture", direction="LR"):
    # External services
    user = Client("User")
    # rabbitmq_management = RabbitMQ("RabbitMQ Management UI")
    rabbitmq_messenger = RabbitMQ("Messenger RabbitMQ")
    rabbitmq_coda = RabbitMQ("CODA RabbitMQ")
    aws_s3 = Storage("AWS S3")
    cks_server1 = Server("CKS Server 1")
    cks_server2 = Server("CKS Server 2")
    scottie = General("Scottie")


    # Databases
    with Cluster("Databases"):
        coda_db = RDSMysqlInstance("CODA DB")
        te_db = RDSMysqlInstance("Estate DB")
        tn_db = RDSMysqlInstance("Tenant DBs")

    # Django Framework
    with Cluster("Django Framework"):
        django = Django("Django Backend")
        django_commands = Django("Django commands")
        django_api = Django("Django API")
        admin = Server("Admin System")
        flower = Server("Celery Flower UI")

    with Cluster("Messenger System"):
        messenger = Server("Messenger Worker")
        service_orchestrator = Policies("Service Orchestrator")

    # GPU Workers (represented as Servers)
    with Cluster("GPU Workers"):
        transcription_worker_X = Server("Transcription Worker X")
        llm_worker_X = Server("LLM Worker X")

    # Non-GPU Worker
    non_gpu_worker = Server("CPU Task Worker")


    # Connections
    user >> django_commands >> django
    user >> django_api >> django
    user >> admin >> django
    user >> flower
    django >> rabbitmq_coda
    django >> messenger
    django >> coda_db
    django >> te_db
    django >> tn_db
    transcription_worker_X >> django
    llm_worker_X >> django
    non_gpu_worker >> coda_db
    messenger >> service_orchestrator >> messenger
    transcription_worker_X >> coda_db
    llm_worker_X >> coda_db
    # commenting out for simplicity
    # transcription_worker_X >> te_db >> transcription_worker_X
    # transcription_worker_X >> tn_db >> transcription_worker_X
    # llm_worker_X >> te_db >> llm_worker_X
    # llm_worker_X >> tn_db >> llm_worker_X


    # Messenger interacts with RabbitMQ
    messenger << rabbitmq_messenger << scottie
    messenger >> rabbitmq_coda

    # Admin interacts with backend and databases
    # admin >> django

    # Workers process tasks from RabbitMQ
    rabbitmq_coda >> transcription_worker_X
    rabbitmq_coda >> llm_worker_X
    rabbitmq_coda >> non_gpu_worker

    # AWS and CKS servers
    transcription_worker_X >> aws_s3
    llm_worker_X >> aws_s3
    transcription_worker_X >> cks_server1
    transcription_worker_X >> cks_server2

    # Celery Flower to monitor tasks
    rabbitmq_coda << flower
    flower >> django

with Diagram("CODA HLD Architecture Basic Message Flow", direction="LR"):
    producer = Client("Producer")
    s3_recordings = S3Glacier("S3 encrypted recordings")
    s3_transcriptions = S3Glacier("S3 encrypted transcriptions")
    tenant_db = Aurora("Tenant DB - parley_transcriptions")

    # testing

    with Cluster("CODA"):
        coda_db = Aurora("CODA DB")
        with Cluster("Messenger"):
            listener = Server("Listener")
            service_orchestrator = Policies("Service Orchestrator")

        with Cluster("CODA workers"):
            async_workers = [
                Server("Transcription Worker X"),
                Server("LLM Worker X"),
                Server("CPU Task Worker")
            ]

    producer >> Relationship('JSON format {"customer_id": int, "parley_id": int, "language": "en", "service_type": ["transcribe","summarise"]}') >> listener >> service_orchestrator >> async_workers
    async_workers << s3_recordings
    async_workers >> s3_transcriptions
    async_workers >> tenant_db


with Diagram("Data Flow Diagram", direction="TB", show=True, ):

    # External Entities
    external_color = "lightblue"
    umony_scottie = Client("Umony Scottie")

    # Orchestration Layer
    orchestration_color = "orange"
    coda_orchestrator = RabbitMQ("CODA Orchestrator")

    # Task Worker Cluster
    with Cluster("Task Workers - Execution Layer", graph_attr={"bgcolor": "lightgreen"}):
        transcription_worker = Server("Transcription Worker")
        summarisation_worker = Server("Summarisation Worker")
        redflagging_worker = Server("RedFlagging Worker")

    # External Storage and Database
    with Cluster("External Storage and Database", graph_attr={"bgcolor": "lightyellow"}):
        customer_s3_transcription = S3("Transcription Bucket")
        customer_s3_recordings = S3("Call Recordings Bucket")
        customer_mysql_db = RDS("Customer DB")

    # Workflow
    workflow_color = "pink"
    workflow_init = Custom("Workflow Initialization", "./icons/workflow_icon.png")

    # Workflow Dataflow
    umony_scottie >> coda_orchestrator  # External message source
    coda_orchestrator >> workflow_init  # Orchestrator starts workflow

    # Task Dataflow
    workflow_init >> transcription_worker
    transcription_worker << customer_s3_recordings
    transcription_worker >> customer_s3_transcription

    workflow_init >> summarisation_worker
    summarisation_worker << customer_s3_transcription
    summarisation_worker >> customer_mysql_db

    workflow_init >> redflagging_worker
    redflagging_worker << customer_s3_transcription
    redflagging_worker >> customer_mysql_db

